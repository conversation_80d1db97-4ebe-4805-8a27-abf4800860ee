export const imageStyleTemplates = {
    none: {
        getPromptInput: () => {
            return ``;
        },
    },
    // Existing styles
    candidPhotography: {
        getPromptInput: () => {
            return `candid photography, blurred background, authentic interaction`;
        },
    },
    streetPhotography: {
        getPromptInput: () => {
            return `street photography, urban environment, authentic moment, documentary style`;
        },
    },

    // Genre Styles
    fantasy: {
        getPromptInput: () => {
            return `fantasy film still, cinematic, magical atmosphere, flowing robes, spell casting`;
        },
    },
    sciFi: {
        getPromptInput: () => {
            return `sci-fi film still, futuristic, action shot, power armor, advanced technology`;
        },
    },
    horror: {
        getPromptInput: () => {
            return `horror cinematic film still, dark, morbid, grotesque, dimly lit, atmospheric`;
        },
    },
    baroque: {
        getPromptInput: () => {
            return `baroque period cinematic film still, ornate, lavishly decorated, classical elegance`;
        },
    },
    matrix: {
        getPromptInput: () => {
            return `cinematic film still from the matrix, fight scene, motion blur, action`;
        },
    },
    gameOfThrones: {
        getPromptInput: () => {
            return `cinematic film still from Game of Thrones, epic fantasy, medieval atmosphere`;
        },
    },
    pixar: {
        getPromptInput: () => {
            return `pixar render, 3d animation, colorful, family-friendly, detailed character design`;
        },
    },
    skyrim: {
        getPromptInput: () => {
            return `screenshot from the videogame Skyrim, fantasy RPG, medieval armor, epic landscape`;
        },
    },
    tokusatsu: {
        getPromptInput: () => {
            return `tokusatsu cinematic film still, action shot, dramatic poses, special effects`;
        },
    },

    // Photography Styles
    cinematicFilmStill: {
        getPromptInput: () => {
            return `cinematic film still, dramatic lighting, professional composition, movie-like quality`;
        },
    },
    amateurPhotography: {
        getPromptInput: () => {
            return `bad quality amateur photo, selfie, casual snapshot, authentic feel`;
        },
    },
    portraitPhotography: {
        getPromptInput: () => {
            return `portrait photography, professional headshot, studio lighting, sharp focus`;
        },
    },
    glamourPhotography: {
        getPromptInput: () => {
            return `glamour photography, luxury backdrop, elegant pose, sophisticated lighting`;
        },
    },
    beautyPhotography: {
        getPromptInput: () => {
            return `beauty photography, airbrushed, flawless skin, soft lighting, elegant`;
        },
    },
    fashionPhotography: {
        getPromptInput: () => {
            return `fashion photography, trendsetting styles, professional model, designer clothing`;
        },
    },
    highkeyPhotography: {
        getPromptInput: () => {
            return `highkey photography, dreamy elegance, ethereal atmosphere, bright lighting`;
        },
    },
    lowkeyPhotography: {
        getPromptInput: () => {
            return `lowkey photography, mysterious, dramatic shadows, single light source`;
        },
    },
    landscapePhotography: {
        getPromptInput: () => {
            return `landscape photography, natural scenery, wide vista, outdoor environment`;
        },
    },
    sportsPhotography: {
        getPromptInput: () => {
            return `sports photography, action shot, dynamic movement, athletic performance`;
        },
    },
    infraredPhotography: {
        getPromptInput: () => {
            return `infrared photography, altered reality, surreal colors, unique perspective`;
        },
    },
    nightPhotography: {
        getPromptInput: () => {
            return `night photography, city lights, dark atmosphere, artificial illumination`;
        },
    },
    flashPhotography: {
        getPromptInput: () => {
            return `flash photography, bright highlights, party atmosphere, candid moments`;
        },
    },
    chiaroscuroPhotography: {
        getPromptInput: () => {
            return `chiaroscuro photography, dramatic light and shadow, rich textures, artistic contrast`;
        },
    },
    minimalistPhotography: {
        getPromptInput: () => {
            return `minimalist photography, negative space, clean composition, simple elegance`;
        },
    },
    surrealistPhotography: {
        getPromptInput: () => {
            return `surrealist photography, bizarre elements, disconnected reality, artistic vision`;
        },
    },
    abstractPhotography: {
        getPromptInput: () => {
            return `abstract photography, surreal backgrounds, artistic interpretation, creative composition`;
        },
    },
    fantasyPhotography: {
        getPromptInput: () => {
            return `fantasy photography, magical elements, misty atmosphere, enchanted setting`;
        },
    },
    loFiPhotography: {
        getPromptInput: () => {
            return `lo-fi photography, vintage feel, retro aesthetic, nostalgic atmosphere`;
        },
    },
    analoguePhotography: {
        getPromptInput: () => {
            return `analogue photography, film grain, light leaks, authentic vintage quality`;
        },
    },
    silhouettePhotography: {
        getPromptInput: () => {
            return `silhouette photography, dramatic contrast, backlit subject, artistic shadow`;
        },
    },
    underwaterPhotography: {
        getPromptInput: () => {
            return `underwater photography, aquatic environment, flowing movement, submerged beauty`;
        },
    },
    photomontage: {
        getPromptInput: () => {
            return `photomontage, layered imagery, surreal juxtaposition, collage effects, narrative elements`;
        },
    },

    // Medium Styles
    filmStill: {
        getPromptInput: () => {
            return `cinematic film still, dramatic lighting, professional cinematography, movie quality`;
        },
    },
    nikonD850: {
        getPromptInput: () => {
            return `photo shot on Nikon D850, professional camera quality, sharp detail, realistic`;
        },
    },
    pixarRender: {
        getPromptInput: () => {
            return `pixar render, 3d animation, colorful, detailed character design, family-friendly`;
        },
    },
    hannaBarberaCartoon: {
        getPromptInput: () => {
            return `hanna barbera cartoon, simple cartoon style, classic animation, retro feel`;
        },
    },
    digitalPainting: {
        getPromptInput: () => {
            return `digital painting, artistic illustration, painted texture, creative interpretation`;
        },
    },
    charcoalInkwash: {
        getPromptInput: () => {
            return `charcoal inkwash drawing, traditional art, smudges, imperfections, artistic texture`;
        },
    },
    vectorIllustration: {
        getPromptInput: () => {
            return `vector digital pop art, clean lines, bold colors, graphic design aesthetic`;
        },
    },

    // Classic & Historical Styles
    vintagePosterArt: {
        getPromptInput: () => {
            return `vintage poster art, bold and colorful graphics, retro advertising style, nostalgic design`;
        },
    },
    artDeco: {
        getPromptInput: () => {
            return `art deco style, bold geometric shapes, luxurious materials, sophisticated vintage-inspired visuals`;
        },
    },
    artNouveau: {
        getPromptInput: () => {
            return `art nouveau style, flowing lines and natural forms, elegant decorative design inspired by nature`;
        },
    },
    chromolithography: {
        getPromptInput: () => {
            return `chromolithography style, vibrant multicolored prints, 19th-century printing techniques`;
        },
    },
    fengShui: {
        getPromptInput: () => {
            return `feng shui style, harmonious and balanced design, Chinese spatial principles, peaceful composition`;
        },
    },
    tenebrism: {
        getPromptInput: () => {
            return `tenebrism style, extreme contrasts of light and dark, dramatic illumination, chiaroscuro effect`;
        },
    },
    ukiyoE: {
        getPromptInput: () => {
            return `ukiyo-e style, traditional Japanese woodblock prints, serene nature scenes, cultural themes`;
        },
    },
    abstractExpressionism: {
        getPromptInput: () => {
            return `abstract expressionism, dynamic nonrepresentational forms, raw emotion and energy`;
        },
    },
    cubism: {
        getPromptInput: () => {
            return `cubism style, abstract geometric shapes, fragmented perspectives, thought-provoking composition`;
        },
    },
    dadaism: {
        getPromptInput: () => {
            return `dadaism style, avant-garde chaotic art, challenging traditional aesthetics, unconventional`;
        },
    },
    deconstructivism: {
        getPromptInput: () => {
            return `deconstructivism style, chaotic and fragmented forms, breaking traditional design rules`;
        },
    },
    ebru: {
        getPromptInput: () => {
            return `ebru style, flowing marbled patterns, water and ink technique, organic elegance`;
        },
    },
    encaustic: {
        getPromptInput: () => {
            return `encaustic art style, hot wax medium, textured and rich painting technique`;
        },
    },
    geometricAbstraction: {
        getPromptInput: () => {
            return `geometric abstraction, abstract shapes and patterns, symmetry and balance, spatial relationships`;
        },
    },
    impasto: {
        getPromptInput: () => {
            return `impasto technique, thick textured brushstrokes, depth and dimensionality in painting`;
        },
    },
    impressionism: {
        getPromptInput: () => {
            return `impressionism style, soft light-filled brushstrokes, fleeting moments, natural beauty`;
        },
    },
    minimalism: {
        getPromptInput: () => {
            return `minimalism style, clean simple design, essential elements, modern uncluttered look`;
        },
    },
    minimalistLandscape: {
        getPromptInput: () => {
            return `minimalist landscape, simple serene natural scenes, clean lines, open space`;
        },
    },
    pentimento: {
        getPromptInput: () => {
            return `pentimento style, layers of reworked art, traces of earlier designs, textured historical effect`;
        },
    },
    symbolism: {
        getPromptInput: () => {
            return `symbolism style, rich symbolic imagery, deeper mystical meanings, expressive art`;
        },
    },
    synthetism: {
        getPromptInput: () => {
            return `synthetism style, flat areas of color, simplified shapes, symbolic and expressive design`;
        },
    },
    watercolor: {
        getPromptInput: () => {
            return `watercolor style, soft translucent washes of color, delicate atmospheric composition`;
        },
    },

    // Digital & Modern Styles
    eightBit: {
        getPromptInput: () => {
            return `8-bit style, retro pixel art, early video game graphics, low-resolution aesthetic`;
        },
    },
    animatedGifArt: {
        getPromptInput: () => {
            return `animated gif art style, playful looping animations, dynamic digital content`;
        },
    },
    cyberpunk: {
        getPromptInput: () => {
            return `cyberpunk style, futuristic neon-lit aesthetics, gritty urban environment, advanced technology`;
        },
    },
    darkSynth: {
        getPromptInput: () => {
            return `dark synth style, neon and dark tones, retro-futuristic aesthetics, synthwave music inspired`;
        },
    },
    diffusion: {
        getPromptInput: () => {
            return `diffusion style, blurred and blended forms, softness and fluidity, ethereal effect`;
        },
    },
    digitalAbstract: {
        getPromptInput: () => {
            return `digital abstract, bold modern design, digital tools, shapes colors and textures`;
        },
    },
    editorialIllustration: {
        getPromptInput: () => {
            return `editorial illustration, stylish expressive art, storytelling, magazine and book style`;
        },
    },
    greebles: {
        getPromptInput: () => {
            return `greebles style, intricate mechanical textures and patterns, sci-fi industrial design`;
        },
    },
    infographicIllustration: {
        getPromptInput: () => {
            return `infographic illustration, clear engaging visuals, data and design combination, educational`;
        },
    },
    lowPoly: {
        getPromptInput: () => {
            return `low poly style, minimalistic 3D design, geometric shapes, clean modern aesthetic`;
        },
    },
    pixelated: {
        getPromptInput: () => {
            return `pixelated style, retro gaming-inspired art, low-resolution pixels, nostalgic aesthetic`;
        },
    },
    risograph: {
        getPromptInput: () => {
            return `risograph style, retro printing technique, layered textures, vibrant colors`;
        },
    },
    vaporwave: {
        getPromptInput: () => {
            return `vaporwave style, retro-futuristic aesthetics, neon tones, 80s nostalgia, surreal vibes`;
        },
    },
    vectorArt: {
        getPromptInput: () => {
            return `vector art style, clean scalable graphics, bold modern design, digital applications`;
        },
    },
    barcodeArtwork: {
        getPromptInput: () => {
            return `barcode artwork, barcode-like patterns, modern design with digital aesthetics`;
        },
    },
    ferrofluid: {
        getPromptInput: () => {
            return `ferrofluid style, abstract fluid-like forms, magnetic movement inspired, organic flow`;
        },
    },
    futurism: {
        getPromptInput: () => {
            return `futurism style, dynamic abstract visuals, speed technology and modernity celebration`;
        },
    },
    gothicArt: {
        getPromptInput: () => {
            return `gothic art style, dark dramatic design, medieval influences, moody intricate visuals`;
        },
    },
    hyperrealism: {
        getPromptInput: () => {
            return `hyperrealism style, extremely detailed lifelike art, reality and illusion blur`;
        },
    },
    magicRealism: {
        getPromptInput: () => {
            return `magic realism style, mundane with fantastical blend, surreal yet grounded imagery`;
        },
    },
    noir: {
        getPromptInput: () => {
            return `noir style, dark shadowy aesthetics, mystery and drama, vintage cinematic vibes`;
        },
    },
    popArt: {
        getPromptInput: () => {
            return `pop art style, bright bold colors, playful themes, comic-inspired retro visuals`;
        },
    },
    popSurrealism: {
        getPromptInput: () => {
            return `pop surrealism style, whimsical fantastical imagery, surrealism with contemporary pop culture`;
        },
    },
    steampunk: {
        getPromptInput: () => {
            return `steampunk style, industrial Victorian elements, futuristic technology, retro-futuristic`;
        },
    },
    surrealism: {
        getPromptInput: () => {
            return `surrealism style, dreamlike bizarre imagery, imaginative otherworldly composition`;
        },
    },
    visionaryArt: {
        getPromptInput: () => {
            return `visionary art style, dreamlike and spiritual, surrealism with transcendent visuals`;
        },
    },

    // Illustration & Drawing Techniques
    artBrut: {
        getPromptInput: () => {
            return `art brut style, raw unrefined art, instinctive unconventional creativity`;
        },
    },
    chibi: {
        getPromptInput: () => {
            return `chibi style, cute exaggerated characters, oversized heads, small bodies, anime aesthetic`;
        },
    },
    doodle: {
        getPromptInput: () => {
            return `doodle style, playful spontaneous drawings, quirky and abstract, casual sketch`;
        },
    },
    claymation: {
        getPromptInput: () => {
            return `claymation style, stop-motion animation, sculpted clay figures, playful tactile visuals`;
        },
    },
    technicalIllustration: {
        getPromptInput: () => {
            return `technical illustration, precise detailed drawings, technical subjects, educational documentation`;
        },
    },
    typographyArt: {
        getPromptInput: () => {
            return `typography art, bold creative use of letters and text, artistic designs and patterns`;
        },
    },
    blueprint: {
        getPromptInput: () => {
            return `blueprint style, technical schematic design, clean structured appearance, engineering drawing`;
        },
    },
    crossHatching: {
        getPromptInput: () => {
            return `cross hatching technique, intersecting lines, texture and shading, traditional drawing method`;
        },
    },
    dotMatrixArt: {
        getPromptInput: () => {
            return `dot matrix art, vintage printing dots, retro and abstract style, halftone pattern`;
        },
    },
    engraving: {
        getPromptInput: () => {
            return `engraving style, intricate carved designs, texture and depth, elegant timeless visuals`;
        },
    },
    calligraphy: {
        getPromptInput: () => {
            return `calligraphy style, beautiful flowing lettering, decorative text, artistic typography`;
        },
    },
    experimentalTypography: {
        getPromptInput: () => {
            return `experimental typography, innovative artistic text use, pushing traditional design boundaries`;
        },
    },
    etchASketch: {
        getPromptInput: () => {
            return `etch-a-sketch style, line art resembling classic drawing toy, nostalgic playful aesthetic`;
        },
    },
    isometricDrawing: {
        getPromptInput: () => {
            return `isometric drawing, three-dimensional objects in two dimensions, technical precise visuals`;
        },
    },
    linocutPrint: {
        getPromptInput: () => {
            return `linocut print style, carved designs printed on paper, bold textured aesthetic`;
        },
    },
    monochromeArt: {
        getPromptInput: () => {
            return `monochrome art, single color creation, texture form and contrast emphasis`;
        },
    },
    pointillism: {
        getPromptInput: () => {
            return `pointillism style, small dots of color, blending together, intricate detailed images`;
        },
    },
    rorschachInkblot: {
        getPromptInput: () => {
            return `rorschach inkblot style, symmetrical abstract forms, ink creation, creative interpretation`;
        },
    },
    stencilArt: {
        getPromptInput: () => {
            return `stencil art style, bold designs with layered shapes and patterns, street art aesthetic`;
        },
    },
    zentangle: {
        getPromptInput: () => {
            return `zentangle style, detailed meditative patterns, intricate mesmerizing composition`;
        },
    },

    // Decorative & Collage Styles
    aboriginalDotPainting: {
        getPromptInput: () => {
            return `aboriginal dot painting, symbolic dot patterns, dreamtime stories, nature connection`;
        },
    },
    alebrijes: {
        getPromptInput: () => {
            return `alebrijes style, brightly colored Mexican folk art, fantastical creatures, vibrant patterns`;
        },
    },
    anthotype: {
        getPromptInput: () => {
            return `anthotype art, botanical art with plant pigments and sunlight, natural unique textures`;
        },
    },
    arabesque: {
        getPromptInput: () => {
            return `arabesque style, intertwining flowing patterns, Islamic decorative art inspiration`;
        },
    },
    batik: {
        getPromptInput: () => {
            return `batik style, traditional fabric patterns, wax-resist dyeing, vibrant intricate design`;
        },
    },
    decorativeArts: {
        getPromptInput: () => {
            return `decorative arts style, ornamental functional design, furniture textiles everyday objects`;
        },
    },
    enameled: {
        getPromptInput: () => {
            return `enameled style, smooth glossy surfaces, vibrant colors, decorative finish`;
        },
    },
    fauxFinish: {
        getPromptInput: () => {
            return `faux finish style, decorative painting techniques, marble or wood mimicking, sophisticated effects`;
        },
    },
    fractal: {
        getPromptInput: () => {
            return `fractal style, complex geometric patterns, infinite repetition, abstract mathematical visuals`;
        },
    },
    generativeDesign: {
        getPromptInput: () => {
            return `generative design, algorithmic patterns and forms, futuristic innovative visuals`;
        },
    },
    gothicRevival: {
        getPromptInput: () => {
            return `gothic revival style, elaborate ornate design, medieval gothic architecture inspiration`;
        },
    },
    grisaille: {
        getPromptInput: () => {
            return `grisaille style, monochromatic art in gray shades, sculptural classical look`;
        },
    },
    kaleidoscopic: {
        getPromptInput: () => {
            return `kaleidoscopic style, symmetrical psychedelic patterns, kaleidoscope view resemblance`;
        },
    },
    kineticArt: {
        getPromptInput: () => {
            return `kinetic art style, motion incorporation, movement illusion, dynamic installations`;
        },
    },
    mandala: {
        getPromptInput: () => {
            return `mandala style, intricate symmetrical patterns, spiritual meditative nature`;
        },
    },
    mosaic: {
        getPromptInput: () => {
            return `mosaic style, small tiles or pieces art, intricate colorful patterns`;
        },
    },
    neuronFlowers: {
        getPromptInput: () => {
            return `neuron flowers style, organic flowing design, neural networks and nature inspiration`;
        },
    },
    prismaArt: {
        getPromptInput: () => {
            return `prisma art style, dynamic light-filled composition, refracted colors and prism effects`;
        },
    },
    sgraffito: {
        getPromptInput: () => {
            return `sgraffito style, decorative designs scratched into surface, contrasting colors revealed`;
        },
    },
    tarotCards: {
        getPromptInput: () => {
            return `tarot cards style, mystical symbolic imagery, archetypes and spiritual themes`;
        },
    },
    collageArt: {
        getPromptInput: () => {
            return `collage art style, layered images and textures, vibrant eclectic mixed media composition`;
        },
    },
    knolling: {
        getPromptInput: () => {
            return `knolling style, arranged ordered objects from above, visually satisfying layouts`;
        },
    },
    mixedMedia: {
        getPromptInput: () => {
            return `mixed media style, layered composition, various materials and techniques, eclectic effects`;
        },
    },
    surrealCollage: {
        getPromptInput: () => {
            return `surreal collage style, fantastical dreamlike composition, unexpected elements and imagery`;
        },
    },
    tornPaperCollage: {
        getPromptInput: () => {
            return `torn paper collage style, layered torn paper pieces, creative textured composition`;
        },
    },
    trashArt: {
        getPromptInput: () => {
            return `trash art style, recycled materials art, sustainability and creativity emphasis`;
        },
    },

    // Nature-Inspired & Organic Styles
    bacteriaArt: {
        getPromptInput: () => {
            return `bacteria art style, microbial cultures grown art, organic experimental visuals`;
        },
    },
    cymatics: {
        getPromptInput: () => {
            return `cymatics style, visual patterns formed by sound vibrations, intricate mesmerizing design`;
        },
    },
    dmtArtStyle: {
        getPromptInput: () => {
            return `dmt art style, psychedelic vivid imagery, altered consciousness states inspiration`;
        },
    },
    inkblot: {
        getPromptInput: () => {
            return `inkblot style, abstract symmetrical designs with ink, imagination and interpretation spark`;
        },
    },
    liquidChrome: {
        getPromptInput: () => {
            return `liquid chrome style, metallic fluid-like surfaces, futuristic sleek aesthetics`;
        },
    },
    prismatic: {
        getPromptInput: () => {
            return `prismatic style, light-filled design, refracted colors and prism effects, vibrancy and depth`;
        },
    },
    sculpturalArt: {
        getPromptInput: () => {
            return `sculptural art style, three-dimensional art, form structure and physicality emphasis`;
        },
    },
    wabiSabi: {
        getPromptInput: () => {
            return `wabi-sabi style, natural imperfection embrace, impermanence and transience, Japanese aesthetic`;
        },
    },
    glassmorphism: {
        getPromptInput: () => {
            return `glassmorphism style, frosted glass effects, vibrant highlights, modern sleek visuals`;
        },
    },
    kirigami: {
        getPromptInput: () => {
            return `kirigami style, paper folding and cutting art, intricate three-dimensional design`;
        },
    },
    quillingArtwork: {
        getPromptInput: () => {
            return `quilling artwork style, rolled and shaped paper, intricate textured composition`;
        },
    },
    feltArt: {
        getPromptInput: () => {
            return `felt art style, soft textured layered wool creation, warmth and handcrafted charm`;
        },
    },
    textileArt: {
        getPromptInput: () => {
            return `textile art style, fabrics threads and textiles design, rich tactile visuals`;
        },
    },
    yarnArt: {
        getPromptInput: () => {
            return `yarn art style, intricate colorful thread patterns, texture and tactile warmth`;
        },
    },

    // Optical & Photographic Styles
    anamorphicArt: {
        getPromptInput: () => {
            return `anamorphic art style, optical illusion art, distorted until specific angle view`;
        },
    },
    diorama: {
        getPromptInput: () => {
            return `diorama style, three-dimensional miniature scenes, storytelling and immersive visuals`;
        },
    },
    opticalIllusion: {
        getPromptInput: () => {
            return `optical illusion style, mind-bending visuals, perspective and perception play, mesmerizing effects`;
        },
    },
    shadowArt: {
        getPromptInput: () => {
            return `shadow art style, light and shadow designs, intricate or surprising patterns`;
        },
    },
    stereogram: {
        getPromptInput: () => {
            return `stereogram style, hidden three-dimensional images, abstract patterns, focus required viewing`;
        },
    },
    trompeLoeil: {
        getPromptInput: () => {
            return `trompe l'oeil style, optical illusions, realistic three-dimensional effects in two-dimensional art`;
        },
    },
    dreamlikePhotography: {
        getPromptInput: () => {
            return `dreamlike photography, surreal atmospheric photos, fantasy and otherworldly vibes`;
        },
    },
    filmNegative: {
        getPromptInput: () => {
            return `film negative style, high-contrast photographic style, traditional film negatives inspiration`;
        },
    },
    infrared: {
        getPromptInput: () => {
            return `infrared style, ethereal surreal photography, unseen infrared light highlighting`;
        },
    },
    splitToning: {
        getPromptInput: () => {
            return `split-toning style, photographic technique, mood enhancement, colors to highlights and shadows`;
        },
    },
    tiltShiftPhotography: {
        getPromptInput: () => {
            return `tilt-shift photography style, miniature effect, selective focus, whimsical dreamlike visuals`;
        },
    },
};
